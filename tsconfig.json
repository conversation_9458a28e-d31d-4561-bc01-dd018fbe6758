{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "types": ["node", "react", "react-dom"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "typeRoots": ["node_modules/@types", "@types"], "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "noImplicitAny": false, "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "context/SearchContext.tsx.remove", "components/ui/sonner.tsx.remove", "components/ui/toggle-group.tsx.remove", "components/ui/toggle.tsx.remove", "components/ui/sidebar.tsx.remove", "components/ui/skeleton.tsx.remove", "components/ui/slider.tsx.remove", "components/ui/table.tsx", "components/ui/tabs.tsx.remove", "components/ui/breadcrumb.tsx.remove", "components/ui/aspect-ratio.tsx.remove", "components/ui/accordion.tsx.remove", "components/ui/sheet.tsx.remove", "components/ui/context-menu.tsx.reomve", "components/ui/dialog.tsx.remove", "components/ui/drawer.tsx.remove", "components/ui/scroll-area.tsx.remove", "components/ui/resizable.tsx.remove", "components/ui/dropdown-menu.tsx.remove", "components/ui/menubar.tsx.remove", "components/ui/hover-card.tsx.remove", "components/ui/navigation-menu.tsx.remove", "components/ui/input-otp.tsx.remove", "components/ui/pagination.tsx.remove", "components/ui/popover.tsx.remove", "components/ui/progress.tsx.remove", "components/ui/use-mobile.tsx.remove", "components/ui/use-toast.ts.remove", "components/ui/command.tsx.remove", "components/ui/carousel.tsx.remove", "components/ui/form.tsx.remove", "hooks/use-mobile.tsx.remove", "hooks/use-toast.ts.remove", "components/analytics/bar-chart.tsx.remove", "components/settings/users/user-form.tsx", "components/settings/users/edit-user.tsx.remove"], "exclude": ["node_modules"]}