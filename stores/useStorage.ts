import { create } from 'zustand';
import { NASVlan, NASRole } from '@/types/interface-type';
import apiClient from '@/lib/apiClient';

type DataStore = {
  users: any[];
  devices: any[];
  loading: boolean;
  nasVlan: NASVlan[];
  nasRole: NASRole[];
  setUsers: (users: any[]) => void;
  setDevices: (devices: any[]) => void;
  setNasVlan: (nasVlan: NASVlan[]) => void;
  setNasRole: (nasRole: NASRole[]) => void;
  setLoading: (state: boolean) => void;

  fetchNasVlans: () => Promise<void>;
  fetchNasRoles: () => Promise<void>;
};

export const useDataStore = create<DataStore>((set, get) => ({
  users: [],
  devices: [],
  nasVlan: [],
  nasRole: [],
  loading: false,

  setUsers: (users) => set({ users }),
  setDevices: (devices) => set({ devices }),
  setNasVlan: (nasVlan) => set({ nasVlan }),
  setNasRole: (nasRole) => set({ nasRole }),
  setLoading: (loading) => set({ loading }),

  fetchNasVlans: async () => {
    if (get().nasVlan.length > 0) return; // prevent refetch
    set({ loading: true });
    try {
      const res = await apiClient.get('/nas/vlan');
      const data = res?.data || [];
      set({ nasVlan: data });
    } catch (err) {
      console.error('Failed to fetch NAS VLANs:', err);
    } finally {
      set({ loading: false });
    }
  },

  fetchNasRoles: async () => {
    if (get().nasRole.length > 0) return; // prevent refetch
    set({ loading: true });
    try {
      const res = await apiClient.get('/nas/role');
      const data = res?.data || [];
      set({ nasRole: data });
    } catch (err) {
      console.error('Failed to fetch NAS Roles:', err);
    } finally {
      set({ loading: false });
    }
  },
}));
