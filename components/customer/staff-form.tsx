"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import jwt from "jsonwebtoken";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { Eye, EyeOff } from "../icons/list";

export interface Package {
  id: number;
  package_name: string;
}

export interface Staff {
  id?: number; // Optional for add mode
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  password?: string; // Optional for edit mode
  pack_id?: string; // This will be the ID, not the name
  org_id?: string; // Optional for edit mode if not always sent
  status: string;
}

interface StaffFormProps {
  staff?: Staff; // Optional staff prop for edit mode
  onClose?: () => void;
  onFormSuccess?: () => void;
}

const getOrgId = () => {
  if (typeof window !== "undefined") {
    const accessToken = Cookies.get("accessToken");
    if (accessToken) {
      try {
        const decoded = jwt.decode(accessToken) as any;
        return decoded?.org_id;
      } catch (e) {
        console.error("Failed to decode access token:", e);
        return null;
      }
    }
  }
  return null;
};

export default function StaffForm({
  staff,
  onClose,
  onFormSuccess,
}: StaffFormProps) {
  const isEditMode = !!staff;
  const router = useRouter();
  const { isRefreshed, setIsRefreshed } = useAuth();
  const org_id_from_cookie = getOrgId();

  const [form, setForm] = useState({
    first_name: staff?.first_name || "",
    last_name: staff?.last_name || "",
    username: staff?.username || "",
    email: staff?.email || "",
    password: "", // Password is always empty initially
    confirmPassword: "",
    pack_id: staff?.pack_id?.toString() || "", // Ensure it's a string ID
    status: staff?.status || "Active",
  });

  const [originalForm, setOriginalForm] = useState(form); // For tracking changes in edit mode
  const [packages, setPackages] = useState<Package[]>([]);
  const [loadingPackages, setLoadingPackages] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [confirmPasswordError, setConfirmPasswordError] = useState("");

  useEffect(() => {
    // Set initial form state for edit mode when 'staff' prop changes
    if (isEditMode && staff) {
      setForm({
        first_name: staff.first_name || "",
        last_name: staff.last_name || "",
        username: staff.username || "",
        email: staff.email || "",
        password: "",
        confirmPassword: "",
        pack_id: staff.pack_id?.toString() || "",
        status: staff.status || "Active",
      });
      setOriginalForm({
        first_name: staff.first_name || "",
        last_name: staff.last_name || "",
        username: staff.username || "",
        email: staff.email || "",
        password: "", // Original form password is not relevant for diffing new password
        confirmPassword: "",
        pack_id: staff.pack_id?.toString() || "",
        status: staff.status || "Active",
      });
    }
  }, [staff, isEditMode]);

  useEffect(() => {
    // Password match validation
    if (form.password && form.confirmPassword) {
      if (form.password !== form.confirmPassword) {
        setConfirmPasswordError("Passwords do not match.");
      } else {
        setConfirmPasswordError("");
      }
    } else {
      setConfirmPasswordError("");
    }
  }, [form.password, form.confirmPassword]);

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await apiClient.get("/package");
        setPackages(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch departments:", error);
        toast.error(error.message || "Failed to fetch departments");
      } finally {
        setLoadingPackages(false);
      }
    };

    fetchPackages();
  }, []);

  const handleChange = (field: keyof typeof form, value: string) => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const getChangedFields = () => {
    const diff: Record<string, any> = {};
    for (const key in form) {
      if (key === "confirmPassword") continue; // Skip confirmPassword
      if (key === "password") {
        if (form.password.trim() !== "") {
          diff.password = form.password.trim();
        }
        continue;
      }
      if (form[key] !== originalForm[key]) {
        diff[key] = form[key];
      }
    }
    return diff;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (form.password !== form.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (
      !isEditMode &&
      (!form.password.trim() || !form.confirmPassword.trim())
    ) {
      toast.error("Password and Confirm Password are required for new staff.");
      return;
    }

    try {
      if (isEditMode) {
        const changedFields = getChangedFields();
        if (Object.keys(changedFields).length === 0) {
          toast.info("No changes detected.");
          onClose?.(); // Close the form if no changes
          return;
        }

        const payload = {
          ...changedFields,
          id: staff?.id, // Ensure ID is part of the payload
        };

        await apiClient.patch(`/customer/${staff?.id}`, payload);
        toast.success("Staff updated successfully!");
      } else {
        const { confirmPassword, ...rest } = form; // Remove confirmPassword from payload
        const payload = {
          ...rest,
          org_id: org_id_from_cookie, // Add org_id for new staff
        };
        await apiClient.post("/customer", payload);
        toast.success("Staff added successfully!");
        router.push("/app/staff"); // Redirect for new staff creation
      }

      setIsRefreshed((prev) => !prev);
      onFormSuccess?.();
      onClose?.();
    } catch (error) {
      console.error(
        `Failed to ${isEditMode ? "edit" : "create"} staff:`,
        error
      );
      const errorMsg =
        error?.response?.data?.errors?.[0]?.msg ||
        error.message ||
        `Failed to ${isEditMode ? "update" : "add"} staff`;
      toast.error(errorMsg);
    }
  };

  const handleCancelClick = () => {
    onClose?.();
  };

  const isValidEmail = (email: string) =>
    email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);

  const isFormValid =
    form.first_name.trim() !== "" &&
    form.last_name.trim() !== "" &&
    form.username.trim() !== "" &&
    form.email.trim() !== "" &&
    isValidEmail(form.email) &&
    form.pack_id.trim() !== "" &&
    (isEditMode
      ? true
      : form.password.trim() !== "" && form.confirmPassword.trim() !== "") && // Password required only for add mode
    confirmPasswordError === "";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-md shadow-2xl overflow-hidden max-w-md md:max-w-[40rem] w-full max-h-[95vh] flex flex-col">
        <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
          <h1 className="text-lg font-extrabold text-center">
            {isEditMode ? "Edit Staff" : "Add New Staff"}
          </h1>
          <p className="text-blue-100 text-sm text-center">
            Create a new user account
            {isEditMode && (
              <>
                &nbsp;for&nbsp;
                <span className="font-semibold text-blue-300">
                  {staff.first_name} {staff.last_name}
                </span>
              </>
            )}
          </p>
        </div>
        <form
          onSubmit={handleSubmit}
          className="flex-grow p-7 grid grid-cols-1 gap-x-5 gap-y-3 overflow-y-auto custom-scrollbar "
        >
          <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="first-name">First Name</Label>
              <Input
                id="first-name"
                type="text"
                value={form.first_name}
                onChange={(e) => handleChange("first_name", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="last-name">Last Name</Label>
              <Input
                id="last-name"
                type="text"
                value={form.last_name}
                onChange={(e) => handleChange("last_name", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                value={form.username}
                onChange={(e) => handleChange("username", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="email-address">Email</Label>
              <Input
                id="email-address"
                type="email"
                value={form.email}
                onChange={(e) => handleChange("email", e.target.value)}
                required
              />
              {form.email && !isValidEmail(form.email) && (
                <span className="text-red-500 text-xs mt-1 block">
                  Please enter a valid email address.
                </span>
              )}
            </div>

            <div className="relative">
              <Label htmlFor="password">
                {isEditMode ? "New Password" : "Password"}
              </Label>
              <Input
                type={showPassword ? "text" : "password"}
                placeholder={
                  isEditMode ? "Leave blank to keep current" : "Enter password"
                }
                value={form.password}
                onChange={(e) => handleChange("password", e.target.value)}
                required={!isEditMode} // Required only for add mode
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-2 h-8 px-2 py-1 text-gray-500 hover:bg-gray-200 rounded-md"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="relative">
              <Label htmlFor="confirmPassword"> Confirm Password</Label>
              <Input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm Password"
                value={form.confirmPassword}
                onChange={(e) =>
                  handleChange("confirmPassword", e.target.value)
                }
                required={!isEditMode} // Required only for add mode
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-2 h-8 px-2 py-1 text-gray-500 hover:bg-gray-200 rounded-md"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
              {confirmPasswordError && (
                <p className="text-red-500 text-xs mt-1 block">
                  {confirmPasswordError}
                </p>
              )}
            </div>

            <PackageSelect
              pack_id={form.pack_id}
              setSelectedPackagesId={(id) => handleChange("pack_id", id)}
              packages={packages}
              loading={loadingPackages}
            />

            <div className="relative">
              <Label htmlFor="status">Status</Label>
              <div className="flex gap-3">
                <button
                  type="button"
                  aria-pressed={form.status === "Active"}
                  className={`w-20 h-8 border-2 rounded-md flex items-center justify-center
          ${
            form.status === "Active"
              ? "border-blue-600 bg-blue-600 text-white"
              : "border-gray-300 bg-white text-black"
          }
          hover:border-blue-400 transition-colors font-medium`}
                  onClick={() => handleChange("status", "Active")}
                >
                  Active
                </button>

                <button
                  type="button"
                  aria-pressed={form.status === "Inactive"}
                  className={`w-20 h-8 border-2 rounded-md flex items-center justify-center
          ${
            form.status === "Inactive"
              ? "border-blue-600 bg-blue-600 text-white"
              : "border-gray-300 bg-white text-black"
          }
          hover:border-blue-400 transition-colors font-medium`}
                  onClick={() => handleChange("status", "Inactive")}
                >
                  Inactive
                </button>
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-end sm:flex-row gap-3 pt-3 border-t border-gray-200 mt-3">
            <Button type="button" variant="outline" onClick={handleCancelClick}>
              Cancel
            </Button>
            <Button
              type="submit"
              className="w-full sm:w-auto"
              disabled={!isFormValid}
            >
              {isEditMode ? "Update Staff" : "Add Staff"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

function PackageSelect({
  pack_id,
  setSelectedPackagesId,
  packages,
  loading,
}: {
  pack_id: string;
  setSelectedPackagesId: (id: string) => void;
  packages: Package[];
  loading: boolean;
}) {
  return (
    <div className="relative">
      <Label htmlFor="Department"> Department</Label>
      <select
        className="w-full p-2 border rounded-md"
        value={pack_id}
        onChange={(e) => setSelectedPackagesId(e.target.value)}
        required
      >
        <option value="">Select a Department</option>
        {loading ? (
          <option disabled>Loading departments...</option>
        ) : (
          packages.map((pkg) => (
            <option key={pkg.id} value={pkg.id.toString()}>
              {pkg.package_name}
            </option>
          ))
        )}
      </select>
    </div>
  );
}
