import React from "react";
import {
  Eye,
  EyeOff,
  User,
  Key,
  Mail, 
  Activity, 
  UserSquare, 
} from "lucide-react";
interface CustomerBasicInfoProps {
  customer: any; // Consider defining a more specific interface for customer
  showPassword: boolean;
  onTogglePassword: () => void; // Added for explicit password toggling
}

const CustomerBasicInfo: React.FC<CustomerBasicInfoProps> = ({
  customer,
  showPassword,
  onTogglePassword,
}) => {
  const customerName =
    customer?.first_name || customer?.last_name
      ? `${customer.first_name || ""} ${customer.last_name || ""}`.trim()
      : "N/A";

  return (
    <div className="bg-white p-4 rounded-lg border shadow-sm text-sm border-blue-500">
      <h3 className="font-semibold mb-3 bg-blue-100 text-blue-800 border-l-4 border-blue-500 p-3 rounded-md">
        Staff Details
      </h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <tbody className="bg-white divide-y divide-gray-100">
            {/* Staff Name */}
            <tr className="hover:bg-gray-50">
              <td className="px-4 py-2 whitespace-nowrap font-medium text-gray-700 flex">
                <User className="w-4 h-4 mr-2 text-blue-500" />
                Staff Name
              </td>
              <td className="px-4 py-2 whitespace-nowrap text-gray-900">
                {customerName}
              </td>
            </tr>

            {/* Username */}
            <tr className="hover:bg-gray-50">
              <td className="px-4 py-2 whitespace-nowrap font-medium text-gray-700 flex">
                <UserSquare className="w-4 h-4 mr-2 text-blue-500" />
                Username
              </td>
              <td className="px-4 py-2 whitespace-nowrap text-gray-900">
                {customer?.username || "N/A"}
              </td>
            </tr>

            {/* Password */}
            <tr className="hover:bg-gray-50">
              <td className="px-4 py-2 whitespace-nowrap font-medium text-gray-700 flex">
                <Key className="w-4 h-4 mr-2 text-blue-500" />
                Password
              </td>
              <td className="px-4 py-2 whitespace-nowrap text-gray-900">
                {customer?.password ? (
                  <div className="flex items-center space-x-2">
                    <span className="font-mono">
                      {showPassword
                        ? customer.password
                        : "•".repeat(Math.min(customer.password.length, 12))}
                    </span>
                    {/* <button
                      onClick={onTogglePassword}
                      className="text-gray-500 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full p-1 transition-colors duration-200"
                      aria-label={
                        showPassword ? "Hide password" : "Show password"
                      }
                      title={showPassword ? "Hide password" : "Show password"}
                    >
                      {showPassword ? (
                        <EyeOff className="w-4 h-4" />
                      ) : (
                        <Eye className="w-4 h-4" />
                      )}
                    </button> */}
                  </div>
                ) : (
                  <span className="text-gray-500 italic">Not set</span>
                )}
              </td>
            </tr>

            {/* Status */}
            <tr className="hover:bg-gray-50">
              <td className="px-4 py-2 whitespace-nowrap font-medium text-gray-700 flex">
                <Activity className="w-4 h-4 mr-2 text-blue-500" />
                Status
              </td>
              <td className="px-4 py-2 whitespace-nowrap text-gray-900">
                {customer?.status ? (
                  <span
                    className={`px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded ${
                      customer.status === "Active"
                        ? "bg-green-500 text-green-100"
                        : "bg-red-200 text-red-700"
                    }`}
                  >
                    {customer.status}
                  </span>
                ) : (
                  "N/A"
                )}
              </td>
            </tr>

            {/* Email */}
            <tr className="hover:bg-gray-50">
              <td className="px-4 py-2 whitespace-nowrap font-medium text-gray-700 flex">
                <Mail className="w-4 h-4 mr-2 text-blue-500" />
                Email
              </td>
              <td className="px-4 py-2 whitespace-nowrap text-gray-900">
                {customer?.email ? (
                  <a
                    href={`mailto:${customer.email}`}
                    className="text-blue-600 hover:underline"
                  >
                    {customer.email}
                  </a>
                ) : (
                  "N/A"
                )}
              </td>
            </tr>
            {/* Department */}
            <tr className="hover:bg-gray-50">
              <td className="px-4 py-2 whitespace-nowrap font-medium text-gray-700 flex">
                <Mail className="w-4 h-4 mr-2 text-blue-500" />
                Department
              </td>
              <td className="px-4 py-2 whitespace-nowrap text-gray-900">
                {customer?.package_name ? (
                  <a
                    href={`mailto:${customer.package_name}`}
                    className="text-blue-600 hover:underline"
                  >
                    {customer.package_name}
                  </a>
                ) : (
                  "N/A"
                )}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CustomerBasicInfo;
