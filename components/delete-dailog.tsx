"use client";
import { useState, useEffect } from "react";
interface deleteProps {
  id: number;
  name: string;
  paraValue: string;
  value: string;
  onDelete: (id: number, name: string) => void | Promise<void>;
  onClose: () => void;
  loading: boolean;
}

export default function DeleteConfirm({
  id,
  name,
  paraValue,
  value,
  onDelete,
  onClose,
  loading,
}: deleteProps) {
  const [inputName, setInputName] = useState("");
  const [error, setError] = useState("");
  const [showError, setShowError] = useState(false);

  const handleClick = () => {
    if (inputName.toLowerCase() === value.toLowerCase()) {
      onDelete(id, name);
      onClose();
    } else {
      setError("Name should match exactly !!!");
    }
  };

  useEffect(() => {
    if (inputName.length > 0 && inputName !== value) {
      setShowError(true);
    } else {
      setShowError(false);
    }
  }, [inputName, value]);

  return (
    <div className="p-8 sm:p-5 space-y-3">
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center backdrop-blur-sm">
        <div className="bg-white p-7 rounded-lg w-auto text-center">
          <h2 className="text-lg font-bold mb-4"> Confirm Deletion</h2>

          <p className="mb-2 text-sm">
            Are you sure you want to delete the {paraValue} member{" "}
            <strong>{value}</strong>?
          </p>
          <p className="mb-4 text-sm text-gray-600">
            Type the {paraValue} name to confirm:
          </p>
          <input
            className="border px-3 py-2 w-full mb-4 rounded-md"
            value={inputName}
            onChange={(e) => {
              setInputName(e.target.value);
              setError("");
            }}
            placeholder={`Enter ${paraValue} name`}
            autoFocus 
          />
          {showError && (
            <p className="text-red-500 text-sm mb-3">
              {paraValue} name does not match
            </p>
          )}
          <div className="flex justify-between gap-2 flex-wrap">
            <button
              onClick={() => {
                onClose();
                setInputName("");
                setShowError(false);
              }}
              className="px-4 py-2 bg-gray-300 rounded-md w-full sm:w-auto"
            >
              Cancel
            </button>
            <button
              onClick={handleClick}
              disabled={inputName !== value}
              className={`px-4 py-2 rounded-md w-full sm:w-auto 
                ${
                  inputName !== value
                    ? "bg-red-300 cursor-not-allowed"
                    : "bg-red-600 text-white"
                }`}
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
