"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import apiClient from "@/lib/apiClient";
import Cookies from "js-cookie";
import jwt from "jsonwebtoken";
import { toast } from "sonner";
import { Eye, EyeOff } from "@/components/icons/list";

export default function UserProfile() {
  const [user, setUser] = useState<any>({});
  const [passwordData, setPasswordData] = useState({
    newPassword: "",
    confirmPassword: "",
  });
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const accessToken = Cookies.get("accessToken");
  const decodedToken = jwt.decode(accessToken) as any;
  const user_id = decodedToken?.user_id;

  const fetchUser = async () => {
    try {
      const response = await apiClient.get(`/user/?id=${user_id}`);
      // Backend returns: { success: true, message: "User details", data: {...} }
      // The apiClient response interceptor extracts the data property
      setUser(response?.data || {});
    } catch (error) {
      console.error("Failed to fetch user:", error);
      toast.error(error.message || "Failed to fetch user profile");
    }
  };

  const handlePasswordChange = async () => {
    // Validation
    if (!passwordData.newPassword.trim()) {
      toast.error("Please enter a new password");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast.error("Password must be at least 6 characters long");
      return;
    }

    setIsUpdatingPassword(true);

    try {
      const userId = user?.id;

      if (!userId) {
        toast.error("User ID not found");
        return;
      }

      await apiClient.patch(`/user/${userId}`, {
        password: passwordData.newPassword,
      });

      toast.success("Password updated successfully!");

      // Clear the password fields
      setPasswordData({
        newPassword: "",
        confirmPassword: "",
      });
    } catch (error) {
      console.error("Failed to update password:", error);
      toast.error(error.message || "Failed to update password. Please try again.");
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  const handleInputChange = (
    field: keyof typeof passwordData,
    value: string
  ) => {
    setPasswordData((prev: typeof passwordData) => ({
      ...prev,
      [field]: value,
    }));
  };

  useEffect(() => {
    fetchUser();
  }, []);

  return (
    <div className="p-5">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10 bg-white p-6 rounded-lg shadow-md ">
        {/* Left: Profile Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6">👤 My Profile</h2>

          {/* Personal Info */}
          <div className="space-y-3 mr-10">
            <div>
              <label className="block text-sm text-black-700 ">Username</label>
              <Input
                className="rounded-full"
                value={user?.username || ""}
                disabled
              />
            </div>
            <div>
              <label className="block text-sm text-black-700">Email</label>
              <Input
                className="rounded-full"
                value={user?.email || ""}
                disabled
              />
            </div>
            <div>
              <label className="block text-sm text-black-700">Group</label>
              <Input
                className="rounded-full"
                value={user?.group || ""}
                disabled
              />
            </div>
          </div>
        </div>

        {/* Right: Change Password */}
        <div>
          <h2 className="text-2xl font-bold mb-6">🔐 Change Password</h2>
          <div className="space-y-3 mr-10">
            <div>
              <label className="text-sm text-black-700">New Password</label>
              <div className="relative">
                <Input
                  className="rounded-full"
                  type={showNewPassword ? "text" : "password"}
                  placeholder="Enter new password"
                  value={passwordData.newPassword}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange("newPassword", e.target.value)
                  }
                  disabled={isUpdatingPassword}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 rounded-full"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  disabled={isUpdatingPassword}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            <div>
              <label className="text-sm text-black-700">Confirm Password</label>
              <div className="relative">
                <Input
                  className="rounded-full"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm new password"
                  value={passwordData.confirmPassword}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange("confirmPassword", e.target.value)
                  }
                  disabled={isUpdatingPassword}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 rounded-full"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isUpdatingPassword}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            <Button
              className="mt-4 rounded-full"
              onClick={handlePasswordChange}
              disabled={
                isUpdatingPassword ||
                !passwordData.newPassword.trim() ||
                !passwordData.confirmPassword.trim()
              }
            >
              {isUpdatingPassword ? "Updating..." : "Update Password"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
