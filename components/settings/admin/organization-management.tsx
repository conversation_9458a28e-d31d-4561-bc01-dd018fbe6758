'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import apiClient from '@/lib/apiClient'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { Building2 } from 'lucide-react'

export interface OrganizationManagementProps {
  organization: {
    id: string
    name: string
  }
  onSubmit: (org: { id: string; name: string }) => void
}

export default function ManageOrganization({
  organization,
  onSubmit,
}: OrganizationManagementProps) {
  const [formData, setFormData] = useState({ name: '' })
  const [originalName, setOriginalName] = useState('')
  const [isValid, setIsValid] = useState(false)

  // Fetch current org data
  useEffect(() => {
    const fetchOrganization = async () => {
      try {
        const res = await apiClient.get("/organization")
        const org = res.data.find((org: any) => org.id === organization.id)
        setOriginalName(org?.name || '')
        setFormData({ name: org?.name || '' })
      } catch (error) {
        console.error('Failed to fetch organization:', error)
        toast.error("Failed to fetch organization")
      }
    }

    fetchOrganization()
  }, [organization.id])


  useEffect(() => {
    setIsValid(formData.name.trim().length > 0 && formData.name !== originalName)
  }, [formData.name, originalName])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ name: e.target.value })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const payload = { name: formData.name.trim() }

    try {
      await apiClient.patch(`/organization/${organization.id}`, payload)
      toast.success('Organization updated successfully')
      onSubmit({ id: organization.id, name: formData.name.trim() })
    } catch (error) {
      console.error('Update error:', error)
      toast.error('Failed to update organization')
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="bg-blue-100 p-2 rounded-lg text-blue-600">
            <Building2 className="w-5 h-5" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Organization Management</h2>
            <p className="text-black text-sm text-center">
            Update organization name for <strong className="text-blue-600">{originalName || 'Unknown'}</strong>.
          </p>
          </div>
        </div>
      </div>
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Organization Name</label>
            <Input
              value={formData.name}
              name="name"
              onChange={handleChange}
              placeholder="Enter organization name"
            />
          </div>
          <div className="pt-4">
            <Button type="submit" className="w-full bg-primary" disabled={!isValid}>
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
