"use client";

import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { useDataStore } from "@/stores/useStorage";
import { NASRole, NASVlan } from "@/types/interface-type";
import { Settings } from "lucide-react";

export default function VlanRoleManagement({
  onSubmit,
}: {
  onSubmit: (data: { role: string; vlan: string }) => void;
}) {
  const [formData, setFormData] = useState({
    role: "",
    vlan: "",
  });

  const { nasRole: roles, nasVlan: vlans } = useDataStore();
  const fetchRoles = useDataStore((state) => state.fetchNasRoles);
  const fetchVlans = useDataStore((state) => state.fetchNasVlans);

  // Fetch shared data if not already loaded
  useEffect(() => {
    if (!roles || roles.length === 0) fetchRoles?.();
    if (!vlans || vlans.length === 0) fetchVlans?.();
  }, [roles, vlans, fetchRoles, fetchVlans]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const isValid = formData.role !== "" && formData.vlan !== "";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isValid) return;

    const payload = {
      role: formData.role.trim(),
      vlan: formData.vlan.trim(),
    };
    onSubmit(payload);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="bg-blue-100 p-2 rounded-lg text-blue-600">
            <Settings className="w-5 h-5" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Default Attributes</h2>
            <p className="text-sm text-gray-600">Set default role and VLAN assignments for new users</p>
          </div>
        </div>
      </div>
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Default Role</label>
            <select
              name="role"
              value={formData.role}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="" disabled>
                Select Role
              </option>
              {roles?.map((role: NASRole) => (
                <option key={role.id} value={role.id}>
                  {role.name}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Default VLAN</label>
            <select
              name="vlan"
              value={formData.vlan}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="" disabled>
                Select VLAN
              </option>
              {vlans?.map((vlan: NASVlan) => (
                <option key={vlan.id} value={vlan.id}>
                  {vlan.name}
                </option>
              ))}
            </select>
          </div>

          <div className="pt-4">
            <Button
              type="submit"
              className="w-full bg-primary"
              disabled={!isValid}
            >
              Set Default Attributes
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
