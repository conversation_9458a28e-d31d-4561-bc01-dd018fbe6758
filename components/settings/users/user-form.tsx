"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/context/AuthContext";
import { useFetch } from "@/hooks/useFetchOnMount";
import { Eye, EyeOff } from "@/components/icons/list";

export type UserGroup = string;
export type UserPackage = string;

export interface Group {
  id: number;
  name: string;
}

export interface Package {
  id: number;
  package_name: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  group: UserGroup;
  password?: string; // Password is optional for edit
  package_?: UserPackage;
  status: string;
  organization: string;
}

interface UserFormProps {
  user?: User; // Optional user prop for edit mode
  onSubmit: (user: any) => void;
  onClose: () => void;
}

export function UserForm({ user, onSubmit, onClose }: UserFormProps) {
  const isEditMode = !!user;

  const [formData, setFormData] = useState({
    username: user?.username || "",
    password: "",
    confirmPassword: "",
    email: user?.email || "",
    package_: user?.package_ || ("" as UserPackage),
    group: user?.group || ("" as UserGroup),
    status: user?.status || "active",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const [selectedGroupId, setSelectedGroupId] = useState("");
  const [selectedPackageId, setSelectedPackageId] = useState("");

  const { org_id } = useAuth();
  const { data: groupsData, loading: loadingGroups } = useFetch("/groups");

  const groups: Group[] = groupsData || [];

  useEffect(() => {
    if (isEditMode && groups.length > 0 && user?.group) {
      const userGroup = groups.find((grp) => grp.name === user.group);
      if (userGroup) {
        setSelectedGroupId(userGroup.id.toString());
      }
    }
  }, [isEditMode, groups, user]);

  useEffect(() => {
    if (formData.password && formData.confirmPassword) {
      if (formData.password !== formData.confirmPassword) {
        setConfirmPasswordError("Passwords do not match.");
      } else {
        setConfirmPasswordError("");
      }
    } else {
      setConfirmPasswordError("");
    }
  }, [formData.password, formData.confirmPassword]);

  const handleChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const getChangedFields = () => {
    const changed: Record<string, any> = {};

    if (isEditMode) {
      changed.id = user?.id;
    }

    if (formData.username.trim() !== (user?.username || "")) {
      changed.username = formData.username.trim();
    }
    if (formData.email.trim() !== (user?.email || "")) {
      changed.email = formData.email.trim();
    }
    if (formData.password && formData.password.trim() !== "") {
      changed.password = formData.password.trim();
    }
    if (
      formData.status.toLowerCase() !== (user?.status || "active").toLowerCase()
    ) {
      changed.status = formData.status.toLowerCase();
    }

    const currentGroupId =
      groups.find((g) => g.id.toString() === selectedGroupId)?.name || "";
    if (currentGroupId !== (user?.group || "")) {
      changed.group_id = selectedGroupId;
    }

    return changed;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isValid) {
      return;
    }

    try {
      let payload: Record<string, any>;
      if (isEditMode) {
        payload = getChangedFields();
        // Add org_id if it's a new field being sent for edit or if it's required for the update
        if (org_id) {
          payload.org_id = org_id.trim();
        }
      } else {
        payload = {
          username: formData.username.trim(),
          password: formData.password.trim(),
          email: formData.email.trim(),
          status: formData.status.toLowerCase(),
          org_id: org_id?.trim(),
          group_id: selectedGroupId.trim(),
          ...(selectedPackageId && { package_id: selectedPackageId.trim() }),
        };
      }
      onSubmit?.(payload);
    } catch (error) {
      console.error(`Failed to ${isEditMode ? "edit" : "add"} user:`, error);
      alert(`Failed to ${isEditMode ? "edit" : "add"} user`);
    }
  };

  const isValid =
    formData.username.trim() !== "" &&
    formData.email.trim() !== "" &&
    formData.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) &&
    selectedGroupId.trim() !== "" &&
    (!isEditMode
      ? formData.password.trim() !== "" &&
        formData.confirmPassword.trim() !== "" &&
        formData.password === formData.confirmPassword
      : formData.password === formData.confirmPassword);

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
        <div className="relative bg-white rounded-md shadow-2xl overflow-hidden max-w-md md:max-w-[37rem] w-full max-h-[90vh] flex flex-col">
          <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
            <h1 className="text-lg font-extrabold text-center">
              {isEditMode ? "Edit User" : "Add New User"}
            </h1>
            <p className="text-blue-100 text-sm text-center">
              {isEditMode
                ? `Update user information for ${
                    user?.username || "Unknown User"
                  }`
                : "Create a new user account and assign them to a group."}
            </p>
          </div>
          <form
            onSubmit={handleSubmit}
            className="flex-grow p-7 grid grid-cols-1 md:grid-cols-2 gap-x-5 gap-y-3 overflow-y-auto custom-scrollbar"
          >
            <div>
              <label
                htmlFor="username"
                className="block text-xs font-semibold text-gray-700"
              >
                Username
              </label>
              <Input
                placeholder="Username"
                value={formData.username}
                onChange={(e) => handleChange("username", e.target.value)}
                autoFocus
              />
            </div>

            <div className="relative">
              <label
                htmlFor="password"
                className="block text-xs font-semibold text-gray-700"
              >
                {isEditMode ? "New Password" : "Password"}
              </label>
              <Input
                type={showPassword ? "text" : "password"}
                placeholder={
                  isEditMode ? "Leave blank to keep current" : "Enter password"
                }
                value={formData.password}
                onChange={(e) => handleChange("password", e.target.value)}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-2 h-8 px-2 py-1 text-gray-500 hover:bg-gray-200 rounded-md"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="relative w-full">
              <label
                htmlFor="confirmPassword"
                className="block text-xs font-semibold text-gray-700 mb-1"
              >
                Confirm Password
              </label>

              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm Password"
                  value={formData.confirmPassword}
                  onChange={(e) =>
                    handleChange("confirmPassword", e.target.value)
                  }
                  className="pr-10 h-10"
                />

                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute top-1/2 right-1 -translate-y-1/2 px-2 py-1 h-8 text-gray-500 hover:bg-gray-200 rounded-md"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              </div>

              {confirmPasswordError && (
                <p className="mt-1 text-xs text-red-500">
                  {confirmPasswordError}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-xs font-semibold text-gray-700"
              >
                Email Address
              </label>
              <Input
                id="email"
                type="email"
                placeholder="e.g. <EMAIL>"
                value={formData.email}
                pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                required
                onChange={(e) => handleChange("email", e.target.value)}
              />
              {formData.email &&
                !formData.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) && (
                  <span className="text-xs text-red-500 mt-1 block">
                    Please enter a valid email address.
                  </span>
                )}
            </div>

            <GroupSelect
              selectedGroupId={selectedGroupId}
              setSelectedGroupId={setSelectedGroupId}
              groups={groups}
              loading={loadingGroups}
            />

            <div>
              <label
                htmlFor="status"
                className="block text-xs font-semibold text-gray-700"
              >
                Status
              </label>
              <div className="flex gap-3">
                <button
                  type="button"
                  aria-pressed={formData.status === "active"}
                  className={`w-20 h-8 border-2 rounded-md flex items-center justify-center
          ${
            formData.status === "active"
              ? "border-blue-600 bg-blue-600 text-white"
              : "border-gray-300 bg-white text-black"
          }
          hover:border-blue-400 transition-colors font-medium`}
                  onClick={() => handleChange("status", "active")}
                >
                  Active
                </button>

                <button
                  type="button"
                  aria-pressed={formData.status === "inactive"}
                  className={`w-20 h-8 border-2 rounded-md flex items-center justify-center
          ${
            formData.status === "inactive"
              ? "border-blue-600 bg-blue-600 text-white"
              : "border-gray-300 bg-white text-black"
          }
          hover:border-blue-400 transition-colors font-medium`}
                  onClick={() => handleChange("status", "inactive")}
                >
                  Inactive
                </button>
              </div>
            </div>

            <div className="md:col-span-2 flex flex-col justify-end sm:flex-row gap-3 pt-3 border-t border-gray-200 mt-3">
              <Button
                type="button"
                variant="custom"
                onClick={onClose}
                className="text-gray-700 hover:text-gray-900 px-4 py-2 rounded-md"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="text-white bg-primary px-6 py-2.5 rounded-md"
                disabled={!isValid}
              >
                {isEditMode ? "Update User" : "Create User"}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}

function GroupSelect({
  selectedGroupId,
  setSelectedGroupId,
  groups,
  loading,
}: {
  selectedGroupId: string;
  setSelectedGroupId: (group: UserGroup) => void;
  groups: Group[];
  loading: boolean;
}) {
  return (
    <div>
      <label
        htmlFor="group"
        className="block text-xs font-semibold text-gray-700"
      >
        Group
      </label>
      <select
        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 ease-in-out text-gray-800 text-xs"
        value={selectedGroupId}
        onChange={(e) => setSelectedGroupId(e.target.value)}
        required
      >
        <option value="">Select a group</option>
        {loading ? (
          <option disabled>Loading groups...</option>
        ) : (
          groups.map((group) => (
            <option key={group.id} value={group.id.toString()}>
              {group.name}
            </option>
          ))
        )}
      </select>
    </div>
  );
}
