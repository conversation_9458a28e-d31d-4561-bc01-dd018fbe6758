"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { NASRole, NASRoleFormData } from "@/types/nas";

interface NASRoleFormProps {
  initialData?: NASRole;
  onSubmit: (data: NASRoleFormData) => void;
  onCancel?: () => void;
  isEdit?: boolean;
}

export default function NASRoleForm({
  initialData,
  onSubmit,
  onCancel,
  isEdit = false,
}: NASRoleFormProps) {
  const [formData, setFormData] = useState<NASRoleFormData>({
    name: initialData?.name || "",
  });

  const [errors, setErrors] = useState<Partial<NASRoleFormData>>({});

  const handleChange = (field: keyof NASRoleFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error for the specific field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined })); // Use undefined to clear
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<NASRoleFormData> = {};

    if (!formData?.name.trim()) {
      newErrors.name = "Role is required"; // Corrected field name
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const getChangedFields = (): Partial<NASRole> => {
    const changed: Partial<NASRole> = {};
    if (initialData?.id) {
      changed.id = initialData.id; // Include ID for existing data
    }

    // Compare formData with initialData to find changes
    if (formData.name.trim() !== initialData?.name) {
      changed.name = formData.name.trim();
    }
    return changed;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      if (isEdit) {
        const changedData = getChangedFields();
        // Ensure you only submit if there are actual changes
        if (Object.keys(changedData).length > (initialData?.id ? 1 : 0)) {
          onSubmit(changedData as NASRoleFormData); // Cast to NASRoleFormData as per onSubmit's type
        } else {
          // If no changes were made in edit mode, just close the form
          onCancel?.();
        }
      } else {
        onSubmit(formData);
      }
    }
  };

  const formTitle = isEdit ? "Edit Network Device Role" : "Add New Network Device Role";
  const formDescription = isEdit
    ? `Update details for ${formData.name || "this role"}.`
    : "Register a new network device role.";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-md shadow-2xl overflow-hidden max-w-md md:max-w-lg w-full max-h-[90vh] flex flex-col">
        <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
          <h1 className="text-lg font-extrabold text-center">
            {isEdit ? "Edit Role" : "Add Role"}
          </h1>
          <p className="text-blue-100 text-sm text-center">
            {isEdit ? (
              <>
                Update role information for{" "}
                <strong className="text-blue-300">
                  {formData.name || "Unknown Role"}
                </strong>
              </>
            ) : (
              "Specify the details of the new role"
            )}
          </p>
        </div>
        <form
          onSubmit={handleSubmit}
          className="flex-grow p-7 grid grid-cols-1 gap-x-5 gap-y-3 overflow-y-auto custom-scrollbar"
        >
          {/* NAS Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Role *</Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleChange("name", e.target.value)}
              placeholder="Enter Role  (e.g. Cambium)"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex  gap-4 pt-4">
             {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
            <Button type="submit" className=" flex-1">
              {isEdit ? "Update Role" : "Add Role"}
            </Button>
           
          </div>
        </form>
      </div>
    </div>
  );
}
