"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { NASVlan, NASVlanFormData } from "@/types/interface-type";

interface NASVlanFormProps {
  initialData?: NASVlan;
  onSubmit: (data: NASVlanFormData) => void;
  onCancel?: () => void;
  isEdit?: boolean;
}

export default function NASVlanForm({
  initialData,
  onSubmit,
  onCancel,
  isEdit = false,
}: NASVlanFormProps) {
  const [formData, setFormData] = useState<NASVlanFormData>({
    name: initialData?.name || "",
    vlan_id: initialData?.vlan_id || "",
  });

  const [errors, setErrors] = useState<Partial<NASVlanFormData>>({});

  const handleChange = (field: keyof NASVlanFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error for the specific field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined })); // Use undefined to clear
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<NASVlanFormData> = {};

    if (!formData?.name) {
      newErrors.name = "Vlan is required"; // Corrected field name
    }
    if (!formData?.vlan_id) {
      newErrors.vlan_id = "Vlan ID is required"; // Corrected field name
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const getChangedFields = (): Partial<NASVlan> => {
    const changed: Partial<NASVlan> = {};
    if (initialData?.id) {
      changed.id = initialData.id; // Include ID for existing data
    }

    // Compare formData with initialData to find changes
    if (formData.name !== initialData?.name) {
      changed.name = formData.name;
    }
    if (formData.vlan_id !== initialData?.vlan_id) {
      changed.vlan_id = formData.vlan_id;
    }

    return changed;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      if (isEdit) {
        const changedData = getChangedFields();
        if (Object.keys(changedData).length > (initialData?.id ? 1 : 0)) {
          onSubmit(changedData as NASVlanFormData); // Cast to NASVlanFormData as per onSubmit's type
        } else {
          // If no changes were made in edit mode, just close the form
          onCancel?.();
        }
      } else {
        onSubmit(formData);
      }
    }
  };

  // const formTitle = isEdit ? "Edit Network Device Vlan" : "Add New Network Device Vlan";
  // const formDescription = isEdit
  //   ? `Update details for ${formData.name || "this vlan"}.`
  //   : "Register a new network device vlan.";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-md shadow-2xl overflow-hidden max-w-md md:max-w-lg w-full max-h-[90vh] flex flex-col">
        <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
          <h1 className="text-lg font-extrabold text-center">
            {isEdit ? "Edit Vlan" : "Add Vlan"}
          </h1>
          <p className="text-blue-100 text-sm text-center">
            {isEdit ? (
              <>
                Update vlan information for{" "}
                <strong className="text-blue-300">
                  {formData.name || "Unknown Vlan"}
                </strong>
              </>
            ) : (
              "Specify the details of the new vlan"
            )}
          </p>
        </div>
        <form
          onSubmit={handleSubmit}
          className="flex-grow p-7 grid grid-cols-1 gap-x-5 gap-y-3 overflow-y-auto custom-scrollbar"
        >
          {/* NAS Name */}
          <div className="space-y-2">
            <Label htmlFor="name">VLAN Name*</Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleChange("name", e.target.value)}
              placeholder="Enter Vlan Name  (e.g. Management)"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="vlan_id">VLAN ID *</Label>
            <Input
              id="vlan_id"
              type="number"
              value={formData.vlan_id}
              onChange={(e) => handleChange("vlan_id", e.target.value)}
              placeholder="Enter Vlan ID  (e.g. 100)"
              className={errors.vlan_id ? "border-red-500" : ""}
            />
            {errors.vlan_id && (
              <p className="text-sm text-red-500">{errors.vlan_id}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex  gap-4 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
            <Button type="submit" className=" flex-1">
              {isEdit ? "Update Vlan" : "Add Vlan"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
