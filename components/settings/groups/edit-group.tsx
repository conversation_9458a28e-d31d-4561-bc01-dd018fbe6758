"use client";

import { React, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { group } from "./group-management";

export interface EditGroupFormProps {
  group: group;
  onSubmit: (group: group) => void;
  onClose: () => void;
}

export function EditGroupForm({
  group,
  onSubmit,
  onClose,
}: EditGroupFormProps) {
  const [new_group_name, setNewGroupName] = useState("");

  const isValid = new_group_name.trim() !== "";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid) {
      const data = { ...group, new_group_name: new_group_name.trim() };
      onSubmit(data);
    }
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6">
  {/* Overlay */}
  <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />

  {/* Modal */}
  <div className="relative bg-white rounded-md shadow-2xl overflow-hidden max-w-md md:max-w-lg w-full max-h-[90vh] flex flex-col">
    
    {/* Header (Gradient) */}
    <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
      <h1 className="text-lg font-extrabold text-center">Edit Group</h1>
      <p className="text-blue-100 text-sm text-center">
        Update group information for <strong className="text-blue-300">{group?.name || "Unknown Group"}</strong>
      </p>
    </div>

    {/* Form content */}
    <form
      onSubmit={handleSubmit}
      className="flex-grow p-6 grid grid-cols-1 gap-x-6 gap-y-6 overflow-y-auto custom-scrollbar"
    >
            <div className="space-y-2">
              <Input
                id="groupName" // Added id for accessibility
                value={new_group_name}
                onChange={(e) => setNewGroupName(e.target.value)}
                placeholder="Enter the Group Name"
                className="w-full" // Ensure input takes full width
                autoFocus
              />
            </div>
            
            <div className="space-y-2">
            <Button type="submit" className="w-full" disabled={!isValid}>
              Save Changes
            </Button>
            <Button variant="custom" onClick={onClose} className="w-full space-y-2">
              Cancel
            </Button>
            </div>
          </form>
          {/* <div className=" pt-4">
            <Button variant="custom" onClick={onClose} className="w-full space-y-2">
              Cancel
            </Button>
          </div> */}
        </div>
      </div>
    </>
  );
}
