"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import {
  GroupTable,
  group,
} from "@/components/settings/groups/group-management";
import { Button } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";
import Cookies from "js-cookie";
import { AddGroupForm } from "@/components/settings/groups/group-management";
import { EditGroupForm } from "@/components/settings/groups/edit-group";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";

export default function GroupManagementPage() {
  const [groups, setGroups] = useState<group[]>();
  const [search, setSearch] = useState("");
  const accessToken = Cookies.get("accessToken");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<group>();
  const { isRefreshed, setIsRefreshed } = useAuth();
  const filteredGroups = groups?.filter((group) =>
    group?.name?.toLowerCase().includes(search.toLowerCase())
  );

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const response = await apiClient.get("/groups");
        setGroups(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch groups:", error);
        toast.error(error.message || "Failed to fetch groups");
      }
    };

    fetchGroups();
  }, [accessToken, isRefreshed]);

  const handleEdit = (group) => {
    setIsEditOpen(true);
    setSelectedGroup(group);
  };
  const handleEditSubmit = async (updatedGroup: group) => {
    const new_group_name = updatedGroup?.new_group_name;
    try {
      const response = await apiClient.patch(`/group/${updatedGroup?.name}`, {
        new_group_name: new_group_name,
      });
      // Backend returns: { success: true, message: "Group has been updated" }
      toast.success("Group Name Edit Successfull");
      setInterval(setIsEditOpen(false), 1000);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update group:", error);
      toast.error(error.message || "Failed to update group");
    }
  };
  const handleSubmit = async (groupname: group) => {
    try {
      const response = await apiClient.post("/group", groupname);
      // Backend returns: { success: true, message: "Created Group" }
      toast.success("Group Added successfully");
      setInterval(setIsAddModalOpen(false), 1000);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to add group:", error);
      toast.error(error.message || "Failed to add group");
    }
  };

  return (
    <div className="p-5 sm:p-5 space-y-3">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
          Group Management
        </h1>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
          <Input
            type="text"
            placeholder="Search Groups"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
          />
          <Button
            className="bg-buttoncolor w-fullsm:w-auto px-3 rounded-full"
            onClick={() => setIsAddModalOpen(true)}
          >
            Add New Group
          </Button>
        </div>
      </div>

      <GroupTable groups={filteredGroups} handleEdit={handleEdit} />
      {isAddModalOpen && (
        <AddGroupForm
          onSubmit={handleSubmit}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}
      {isEditOpen && (
        <EditGroupForm
          onClose={() => setIsEditOpen(false)}
          group={selectedGroup}
          onSubmit={handleEditSubmit}
        />
      )}
    </div>
  );
}
