"use client";
import CustomerView from "@/components/customer/view-customer";
import { useSearchParams } from "next/navigation";

import { useFetch } from "@/hooks/useFetchOnMount";
import { CustomerInfo } from "@/types/interface-type";

export default function CustomerViewPage() {
  const searchParams = useSearchParams();
  const id = searchParams.get("id");

  const { data: userData } = useFetch("customer");

  let custData: CustomerInfo[] = userData;

  const selectedSession = custData.find((data) => data.id === Number(id));

  const select = () => {};

  return (
    <div className="p-5 mx-auto ">
      <CustomerView
        selectedCustomer={selectedSession}
        setSelectedCustomer={select}
      ></CustomerView>
    </div>
  );
}
